import os
from PIL import Image
import json
from modelscope import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor
from qwen_vl_utils import process_vision_info
import torch

base_dir = '~/模块C/resource/药品信息'
resized_dir = '~/模块C/resource/药品信息/resized'
model_dir = "~/模块C/resource/Qwen2-VL-2B-Instruct"
os.makedirs(resized_dir, exist_ok=True)

model = Qwen2VLForConditionalGeneration.from_pretrained(model_dir, torch_dtype="auto", device_map="auto")
processor = AutoProcessor.from_pretrained(model_dir)

# <1> resize函数：将图片 resize 到合适大小 两处代码共1.5分
def resize_image(input_path, output_path, max_size=____):
    try:
        img = Image.open(input_path)
        _____________________________
        _____________________________
        img.save(output_path, format='JPEG')
        return True
    except Exception as e:
        print(f"Failed to process {input_path}: {e}")
        return False

train_data = []

# <2> 读取子文件夹图片resize并找出药品正面图 两处代码共1分
for sub in os.listdir(base_dir):
    sub_path = os.path.join(base_dir, sub)
    if not os.path.isdir(sub_path):
        continue

    imgs = [fname for fname in sorted(os.listdir(sub_path))
            if fname.lower().endswith(('.jpeg'))]
    if not imgs:
        continue

    front_candidates = ________
    front_name = None
    for name in imgs:
        if name in front_candidates:
            front_name = name
            break
    if front_name is None:
        front_name = imgs[0]

    resized_paths_all = []
    resized_front_path = None

    for name in imgs:
        orig_path = os.path.join(sub_path, name)
        out_sub_dir = os.path.join(resized_dir, sub)
        os.makedirs(out_sub_dir, exist_ok=True)
        out_path = os.path.join(out_sub_dir, name)
        success = ________________________________
        if success:
            uri = f"file://{out_path}"
            resized_paths_all.append(uri)
            if name == front_name:
                resized_front_path = uri

    if resized_front_path is None:
        print(f"子文件夹 {sub} 未能找到并 resize 出正面图片，跳过")
        continue

# <3> 构造 messages，模型多图推理 4处代码共2分

    user_instruction = _________________________________________________________
    content = []
    for p in resized_paths_all:
        content.append(________________________________)
    content.append({"type": "text", "text": user_instruction})
    messages = [
        {
            "role": "user",
            "content": content
        }
    ]
    
    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
    image_inputs, video_inputs = process_vision_info(messages)
    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt"
    )
    inputs = inputs.to(model.device)
    
    with torch.no_grad():
        generated_ids = model.generate(**inputs, max_new_tokens=_____)
    
    generated_ids_trimmed = [
        out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
    ]
    output_texts = processor.batch_decode(
        generated_ids_trimmed,
        skip_special_tokens=True,
        clean_up_tokenization_spaces=False
    )
    answer = _______________________

# <4> 构造并保存训练集，3处代码共1.5分
    entry = {
        "conversations": [
            {"from": "human", "value": user_instruction},
            {"from": "gpt", "value": answer}
        ],
        "images": ____________________
    }
    train_data.append(entry)


output_json_path = _______________________
with open(output_json_path, 'w', encoding='utf-8') as f:
    json.dump(_______________________)

print(f"共处理 {len(train_data)} 个子文件夹，结果保存到 {output_json_path}")

# <5>  检查生成的药品信息，并在 LLaMA-Factory/data/dataset_info.json 中仿照已有数据格式补充填写新生成的json数据，并将新生成的 train.json 放入 LLaMA-Factory/data 中。1分。

# <6>  填充examples/train_lora/qwen2vl_lora_sft.yaml中相应参数，7处代码各0.5分，共3.5分。

### model
model_name_or_path: ~/模块C/resource/Qwen2-VL-2B-Instruct
image_max_pixels: ______
trust_remote_code: true

### method
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 8
lora_target: all

### dataset
dataset: _________ 
template: qwen2_vl
cutoff_len: 2048
max_samples: _________
overwrite_cache: true
preprocessing_num_workers: 16
dataloader_num_workers: 4

### output
output_dir: ~/模块C/ans/sft_model
logging_steps: 10
save_steps: 500
plot_loss: true
overwrite_output_dir: true
save_only_model: false
report_to: none  

### train
per_device_train_batch_size: ___
gradient_accumulation_steps: ___
learning_rate: ___
num_train_epochs: ___
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true
ddp_timeout: 180000000
resume_from_checkpoint: null

### eval
# val_size: 0.1
# per_device_eval_batch_size: 1
# eval_strategy: steps
# eval_steps: 500

# <7> 开始训练 llamafactory-cli train examples/train_lora/qwen2vl_lora_sft.yaml 

# <8> 确认 examples/merge_lora/llama3_lora_sft.yaml 中相应参数，进行模型导出。0.5分

### model
model_name_or_path: ~/模块C/resource/Qwen2-VL-2B-Instruct
adapter_name_or_path: ~/模块C/ans/sft_model
template: qwen2_vl
trust_remote_code: true

### export
export_dir: ~/模块C/ans/qwen2vl_lora_sft
export_size: 5
export_device: auto  
export_legacy_format: false

# <9> 根据 Accuracy 及 规范输出数量 进行打分
# 相同分数情况下可比较规范输出数量

| Accuracy 范围         | 得分  |
|----------------------|-------|
| ≤ 0.60               | 0     |
| 0.60 < Accuracy ≤ 0.65 | 1     |
| 0.65 < Accuracy ≤ 0.70 | 1.5   |
| 0.70 < Accuracy ≤ 0.75 | 2     |
| 0.75 < Accuracy ≤ 0.80 | 2.5   |
| 0.80 < Accuracy ≤ 0.85 | 3     |
| > 0.85               | 3.5   |

# <10> 参考 "~/模块C/resource/rknn-llm/tree/main/examples/Qwen2-VL_Demo"文档进行模型转换和部署推理 共3分
# 模型转换（rknn、rkllm转换成功 1分）
# 模型编译 1分
# 模型部署、推理 1分

# <11> 每题0.5分 共2.5分